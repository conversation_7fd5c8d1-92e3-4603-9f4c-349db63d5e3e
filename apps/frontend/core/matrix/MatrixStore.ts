/**
 * 统一矩阵状态管理
 * 🎯 核心价值：单一数据源，数据驱动视图，高性能计算属性
 * 📦 功能范围：矩阵数据、配置管理、计算属性、性能监控
 * 🔄 架构设计：基于Zustand的响应式状态管理，支持持久化和计算属性缓存
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { produce, enableMapSet } from 'immer';

// 启用 Immer 的 MapSet 插件以支持 Map 和 Set 数据结构
enableMapSet();
import type {
  MatrixData,
  MatrixConfig,
  CellData,
  ProcessedMatrixData,
  ComputedCache,
  PerformanceMetrics,
  BusinessMode,
  Coordinate,
  ColorType,
} from './MatrixTypes';

import {
  DEFAULT_MATRIX_CONFIG,
  coordinateKey,
  createDefaultCell,
  MATRIX_SIZE,
} from './MatrixTypes';

// A组数据已移除 - 创建空的数据结构
interface MatrixDataSet {
  points: any[];
  byColor: Map<string, any[]>;
  byLevel: Map<number, any[]>;
  byGroup: Map<string, any[]>;
  byCoordinate: Map<string, any>;
  metadata: {
    totalPoints: number;
    colorCounts: Record<string, number>;
    levelCounts: Record<number, number>;
    groupCounts: Record<string, number>;
    lastUpdate: number;
  };
}

// 创建空的矩阵数据集
const createEmptyMatrixDataSet = (): MatrixDataSet => ({
  points: [],
  byColor: new Map(),
  byLevel: new Map(),
  byGroup: new Map(),
  byCoordinate: new Map(),
  metadata: {
    totalPoints: 0,
    colorCounts: {},
    levelCounts: {},
    groupCounts: {},
    lastUpdate: Date.now(),
  },
});

// 空的数据查询函数
const getMatrixDataByCoordinate = (_data: MatrixDataSet, _x: number, _y: number) => null;
const hasMatrixData = (_data: MatrixDataSet, _x: number, _y: number) => false;

import { matrixCore } from './MatrixCore';

// ===== 状态接口 =====

interface MatrixStoreState {
  // 核心数据
  data: MatrixData;
  config: MatrixConfig;

  // 完整矩阵数据（A-M组）
  matrixData: MatrixDataSet;

  // 计算属性缓存
  cache: ComputedCache;

  // 性能监控
  metrics: PerformanceMetrics;

  // 状态标识
  isLoading: boolean;
  isDirty: boolean;
  lastUpdate: number;
}

interface MatrixStoreActions {
  // 数据操作
  initializeMatrix: () => void;
  updateCell: (x: number, y: number, updates: Partial<CellData>) => void;
  updateCells: (updates: Array<{ x: number; y: number; data: Partial<CellData> }>) => void;
  clearMatrix: () => void;
  
  // 配置操作
  setMode: (mode: BusinessMode) => void;
  
  // 交互操作
  selectCell: (x: number, y: number, multiSelect?: boolean) => void;
  selectCells: (coordinates: Coordinate[]) => void;
  clearSelection: () => void;
  hoverCell: (x: number, y: number) => void;
  focusCell: (x: number, y: number) => void;
  
  // 计算属性
  getProcessedData: () => ProcessedMatrixData;
  getCellRenderData: (x: number, y: number) => any;
  
  // 矩阵数据操作
  getMatrixDataByCoordinate: (x: number, y: number) => any;
  hasMatrixData: (x: number, y: number) => boolean;

  // 调试和分析工具
  getMatrixDataStats: () => any;
  analyzeQueryPerformance: (sampleSize?: number) => any;

  // 缓存管理
  invalidateCache: () => void;
  updateCache: () => void;

  // 性能监控
  startPerformanceTracking: () => void;
  endPerformanceTracking: (operation: string) => void;
}

type MatrixStore = MatrixStoreState & MatrixStoreActions;

// ===== 初始状态 =====

const createInitialData = (): MatrixData => ({
  cells: new Map(),
  selectedCells: new Set(),
  hoveredCell: null,
  focusedCell: null,
});

const createInitialCache = (): ComputedCache => ({
  cellStyles: new Map(),
  cellContents: new Map(),
  cellClassNames: new Map(),
  interactionStates: new Map(),
  lastUpdate: 0,
});

const createInitialMetrics = (): PerformanceMetrics => ({
  renderTime: 0,
  updateTime: 0,
  cacheHitRate: 0,
  memoryUsage: 0,
  frameRate: 60,
});

// ===== 计算属性函数 =====

const computeCellContent = (cell: CellData, config: MatrixConfig): string => {
  switch (config.mode) {
    case 'coordinate':
      return `${cell.x},${cell.y}`;
    case 'color':
      return cell.color || '';
    case 'value':
      return cell.value?.toString() || '';
    case 'word':
      return cell.word || '';
    default:
      return '';
  }
};

const computeCellStyle = (cell: CellData): any => {
  const baseStyle: any = {
    width: '20px',
    height: '20px',
  };

  if (cell.isSelected) {
    baseStyle.border = '2px solid #3b82f6';
  }

  if (cell.isHovered) {
    baseStyle.backgroundColor = '#f3f4f6';
  }

  if (cell.color) {
    const colorMap = {
      red: '#ef4444',
      cyan: '#06b6d4',
      yellow: '#eab308',
      purple: '#a855f7',
      orange: '#f97316',
      green: '#22c55e',
      blue: '#3b82f6',
      pink: '#ec4899',
    };
    baseStyle.backgroundColor = colorMap[cell.color];
  }

  return baseStyle;
};

const computeCellClassName = (cell: CellData, _config: MatrixConfig): string => {
  const classes = ['matrix-cell'];
  
  if (cell.isActive) classes.push('active');
  if (cell.isSelected) classes.push('selected');
  if (cell.isHovered) classes.push('hovered');
  if (cell.color) classes.push(`color-${cell.color}`);
  if (cell.level) classes.push(`level-${cell.level}`);
  
  return classes.join(' ');
};

// ===== Store实现 =====

export const useMatrixStore = create<MatrixStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      data: createInitialData(),
      config: DEFAULT_MATRIX_CONFIG,
      matrixData: createEmptyMatrixDataSet(),
      cache: createInitialCache(),
      metrics: createInitialMetrics(),
      isLoading: false,
      isDirty: false,
      lastUpdate: Date.now(),
      
      // 数据操作
      initializeMatrix: () => {
        const startTime = performance.now();

        set(produce((state) => {
          state.isLoading = true;
          state.data.cells.clear();

          // 获取矩阵数据统计信息
          const totalDataPoints = state.matrixData.metadata.totalPoints;
          const dataPointsMap = state.matrixData.byCoordinate;

          console.log(`开始初始化矩阵 - 数据点总数: ${totalDataPoints}, 网格大小: ${MATRIX_SIZE}x${MATRIX_SIZE}`);

          // 批量初始化所有单元格
          const cells = new Map<string, CellData>();
          let processedCells = 0;
          let cellsWithData = 0;

          for (let x = 0; x < MATRIX_SIZE; x++) {
            for (let y = 0; y < MATRIX_SIZE; y++) {
              const key = coordinateKey(x, y);
              const cell = createDefaultCell(x, y);

              // 直接从预建索引中查找数据点（高性能）
              const matrixDataPoint = dataPointsMap.get(key);

              if (matrixDataPoint) {
                cellsWithData++;
                // 过滤掉黑色，因为CellData.color不包含黑色
                if (matrixDataPoint.color !== 'black') {
                  cell.color = matrixDataPoint.color as ColorType;
                }
                cell.level = matrixDataPoint.level;
                cell.value = matrixDataPoint.level; // 设置数值为层级
              }

              cells.set(key, cell);
              processedCells++;
            }
          }

          // 批量设置到状态中
          state.data.cells = cells;
          state.isDirty = true;
          state.lastUpdate = Date.now();
          state.isLoading = false;

          console.log(`矩阵初始化完成 - 处理单元格: ${processedCells}, 包含数据的单元格: ${cellsWithData}`);
        }));

        const endTime = performance.now();
        const initTime = endTime - startTime;
        get().endPerformanceTracking(`initializeMatrix: ${initTime.toFixed(2)}ms`);

        // 性能警告
        if (initTime > 100) {
          console.warn(`⚠️ 矩阵初始化耗时 ${initTime.toFixed(2)}ms，超过预期的100ms阈值`);
        } else {
          console.log(`✅ 矩阵初始化性能良好: ${initTime.toFixed(2)}ms`);
        }
      },
      
      updateCell: (x: number, y: number, updates: Partial<CellData>) => {
        set(produce((state) => {
          const key = coordinateKey(x, y);
          const cell = state.data.cells.get(key);
          
          if (cell) {
            Object.assign(cell, updates);
            state.isDirty = true;
            state.lastUpdate = Date.now();
          }
        }));
        
        get().invalidateCache();
      },
      
      updateCells: (updates) => {
        set(produce((state) => {
          updates.forEach(({ x, y, data }) => {
            const key = coordinateKey(x, y);
            const cell = state.data.cells.get(key);
            
            if (cell) {
              Object.assign(cell, data);
            }
          });
          
          state.isDirty = true;
          state.lastUpdate = Date.now();
        }));
        
        get().invalidateCache();
      },
      
      clearMatrix: () => {
        set(produce((state) => {
          state.data.cells.clear();
          state.data.selectedCells.clear();
          state.data.hoveredCell = null;
          state.data.focusedCell = null;
          state.isDirty = true;
          state.lastUpdate = Date.now();
        }));
        
        get().invalidateCache();
      },
      
      // 配置操作
      setMode: (mode: BusinessMode) => {
        set(produce((state) => {
          state.config.mode = mode;
          state.isDirty = true;
          state.lastUpdate = Date.now();
        }));
        
        get().invalidateCache();
      },
      

      
      // 交互操作
      selectCell: (x: number, y: number, multiSelect = false) => {
        set(produce((state) => {
          const key = coordinateKey(x, y);
          
          if (!multiSelect) {
            state.data.selectedCells.clear();
            // 清除所有单元格的选中状态
            state.data.cells.forEach((cell: CellData) => {
              cell.isSelected = false;
            });
          }
          
          state.data.selectedCells.add(key);
          const cell = state.data.cells.get(key);
          if (cell) {
            cell.isSelected = true;
          }
          
          state.lastUpdate = Date.now();
        }));
      },
      
      selectCells: (coordinates: Coordinate[]) => {
        set(produce((state) => {
          state.data.selectedCells.clear();
          
          // 清除所有选中状态
          state.data.cells.forEach((cell: CellData) => {
            cell.isSelected = false;
          });
          
          // 设置新的选中状态
          coordinates.forEach(({ x, y }) => {
            const key = coordinateKey(x, y);
            state.data.selectedCells.add(key);
            const cell = state.data.cells.get(key);
            if (cell) {
              cell.isSelected = true;
            }
          });
          
          state.lastUpdate = Date.now();
        }));
      },
      
      clearSelection: () => {
        set(produce((state) => {
          state.data.selectedCells.clear();
          state.data.cells.forEach((cell: CellData) => {
            cell.isSelected = false;
          });
          state.lastUpdate = Date.now();
        }));
      },
      
      hoverCell: (x: number, y: number) => {
        set(produce((state) => {
          // 清除之前的悬停状态
          if (state.data.hoveredCell) {
            const prevCell = state.data.cells.get(state.data.hoveredCell);
            if (prevCell) {
              prevCell.isHovered = false;
            }
          }
          
          // 设置新的悬停状态
          const key = coordinateKey(x, y);
          state.data.hoveredCell = key;
          const cell = state.data.cells.get(key);
          if (cell) {
            cell.isHovered = true;
          }
          
          state.lastUpdate = Date.now();
        }));
      },
      
      focusCell: (x: number, y: number) => {
        set(produce((state) => {
          const key = coordinateKey(x, y);
          state.data.focusedCell = key;
          state.lastUpdate = Date.now();
        }));
      },
      
      // 计算属性
      getProcessedData: (): ProcessedMatrixData => {
        const state = get();
        // 使用矩阵核心引擎处理数据
        return matrixCore.processData(state.data, state.config);
      },
      
      getCellRenderData: (x: number, y: number) => {
        const state = get();
        const key = coordinateKey(x, y);
        const cell = state.data.cells.get(key);
        
        if (!cell) return null;
        
        return {
          content: computeCellContent(cell, state.config),
          style: computeCellStyle(cell),
          className: computeCellClassName(cell, state.config),
          isInteractive: cell.isActive,
        };
      },
      
      // 缓存管理
      invalidateCache: () => {
        set(produce((state) => {
          state.cache.cellStyles.clear();
          state.cache.cellContents.clear();
          state.cache.cellClassNames.clear();
          state.cache.interactionStates.clear();
          state.cache.lastUpdate = Date.now();
        }));
      },
      
      updateCache: () => {
        const state = get();
        const processedData = state.getProcessedData();
        
        set(produce((draft) => {
          draft.cache.cellStyles.clear();
          draft.cache.cellContents.clear();
          draft.cache.cellClassNames.clear();
          
          processedData.renderData.forEach((renderData, key) => {
            draft.cache.cellStyles.set(key, renderData.style);
            draft.cache.cellContents.set(key, renderData.content);
            draft.cache.cellClassNames.set(key, renderData.className);
          });
          
          draft.cache.lastUpdate = Date.now();
        }));
      },
      
      // 矩阵数据操作
      getMatrixDataByCoordinate: (x: number, y: number) => {
        return getMatrixDataByCoordinate(get().matrixData, x, y);
      },

      hasMatrixData: (x: number, y: number) => {
        return hasMatrixData(get().matrixData, x, y);
      },

      // 调试和分析工具
      getMatrixDataStats: () => {
        const state = get();
        const { metadata, byCoordinate } = state.matrixData;

        // 分析数据覆盖范围
        let minX = MATRIX_SIZE, maxX = -1, minY = MATRIX_SIZE, maxY = -1;
        const coordinateRanges: Record<string, { count: number; coordinates: string[] }> = {};

        byCoordinate.forEach((_, key) => {
          const [x, y] = key.split(',').map(Number);
          minX = Math.min(minX, x);
          maxX = Math.max(maxX, x);
          minY = Math.min(minY, y);
          maxY = Math.max(maxY, y);

          // 按行分组统计
          const rowKey = `row_${y}`;
          if (!coordinateRanges[rowKey]) {
            coordinateRanges[rowKey] = { count: 0, coordinates: [] };
          }
          coordinateRanges[rowKey].count++;
          coordinateRanges[rowKey].coordinates.push(key);
        });

        return {
          totalDataPoints: metadata.totalPoints,
          coverageRange: {
            x: { min: minX, max: maxX, span: maxX - minX + 1 },
            y: { min: minY, max: maxY, span: maxY - minY + 1 }
          },
          colorDistribution: metadata.colorCounts,
          levelDistribution: metadata.levelCounts,
          groupDistribution: metadata.groupCounts,
          rowCoverage: coordinateRanges,
          coveragePercentage: ((metadata.totalPoints / (MATRIX_SIZE * MATRIX_SIZE)) * 100).toFixed(2) + '%'
        };
      },

      // 性能分析工具
      analyzeQueryPerformance: (sampleSize: number = 100) => {
        const state = get();
        const startTime = performance.now();

        // 随机采样查询
        const results = {
          totalQueries: sampleSize,
          successfulQueries: 0,
          failedQueries: 0,
          averageQueryTime: 0,
          queryTimes: [] as number[]
        };

        for (let i = 0; i < sampleSize; i++) {
          const x = Math.floor(Math.random() * MATRIX_SIZE);
          const y = Math.floor(Math.random() * MATRIX_SIZE);

          const queryStart = performance.now();
          const result = getMatrixDataByCoordinate(state.matrixData, x, y);
          const queryEnd = performance.now();

          const queryTime = queryEnd - queryStart;
          results.queryTimes.push(queryTime);

          if (result) {
            results.successfulQueries++;
          } else {
            results.failedQueries++;
          }
        }

        const totalTime = performance.now() - startTime;
        results.averageQueryTime = results.queryTimes.reduce((a, b) => a + b, 0) / sampleSize;

        console.log('🔍 查询性能分析结果:', {
          ...results,
          totalAnalysisTime: `${totalTime.toFixed(2)}ms`,
          successRate: `${((results.successfulQueries / sampleSize) * 100).toFixed(1)}%`
        });

        return results;
      },

      // 性能监控
      startPerformanceTracking: () => {
        // 性能监控开始
      },

      endPerformanceTracking: (operation: string) => {
        console.log(`Performance: ${operation}`);
      },
    }),
    {
      name: 'matrix-store',
      version: 1,
      // 只持久化配置和基础数据，不持久化缓存
      partialize: (state) => ({
        config: state.config,
        data: {
          ...state.data,
          cells: Array.from(state.data.cells.entries()),
          selectedCells: Array.from(state.data.selectedCells),
        },
      }),
      // 反序列化时重建Map和Set
      onRehydrateStorage: () => (state) => {
        if (state?.data) {
          state.data.cells = new Map(state.data.cells as any);
          state.data.selectedCells = new Set(state.data.selectedCells as any);
        }
      },
    }
  )
);

// ===== 选择器钩子 =====

export const useMatrixData = () => useMatrixStore((state) => state.data);
export const useMatrixConfig = () => useMatrixStore((state) => state.config);
export const useMatrixMode = () => useMatrixStore((state) => state.config.mode);
export const useSelectedCells = () => useMatrixStore((state) => state.data.selectedCells);
export const useHoveredCell = () => useMatrixStore((state) => state.data.hoveredCell);
export const useFocusedCell = () => useMatrixStore((state) => state.data.focusedCell);
export const useMatrixMetrics = () => useMatrixStore((state) => state.metrics);
export const useIsMatrixDirty = () => useMatrixStore((state) => state.isDirty);
