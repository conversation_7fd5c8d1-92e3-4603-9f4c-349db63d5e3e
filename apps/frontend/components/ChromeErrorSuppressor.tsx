'use client';

import { useEffect } from 'react';

/**
 * Chrome扩展错误抑制器组件
 * 用于抑制Chrome扩展相关的runtime.lastError警告
 */
export function ChromeErrorSuppressor() {
  useEffect(() => {
    // 只在开发环境中运行
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    // 保存原始的console.warn方法
    const originalWarn = console.warn;
    const originalError = console.error;

    // 重写console.warn以过滤Chrome扩展警告
    console.warn = (...args: any[]) => {
      const message = args[0]?.toString?.() || '';
      
      // 过滤Chrome扩展相关的警告
      if (
        message.includes('runtime.lastError') ||
        message.includes('Could not establish connection') ||
        message.includes('Receiving end does not exist') ||
        message.includes('Extension context invalidated') ||
        message.includes('chrome-extension://')
      ) {
        return; // 忽略这些警告
      }
      
      // 其他警告正常显示
      originalWarn.apply(console, args);
    };

    // 重写console.error以过滤Chrome扩展错误
    console.error = (...args: any[]) => {
      const message = args[0]?.toString?.() || '';
      
      // 过滤Chrome扩展相关的错误
      if (
        message.includes('runtime.lastError') ||
        message.includes('Could not establish connection') ||
        message.includes('Receiving end does not exist') ||
        message.includes('Extension context invalidated') ||
        message.includes('chrome-extension://')
      ) {
        return; // 忽略这些错误
      }
      
      // 其他错误正常显示
      originalError.apply(console, args);
    };

    // 添加全局错误事件监听器
    const handleError = (event: ErrorEvent) => {
      const message = event.message || '';
      
      if (
        message.includes('runtime.lastError') ||
        message.includes('Could not establish connection') ||
        message.includes('Receiving end does not exist') ||
        message.includes('Extension context invalidated') ||
        message.includes('chrome-extension://')
      ) {
        event.preventDefault();
        event.stopPropagation();
        return false;
      }
    };

    // 添加未处理的Promise拒绝监听器
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const reason = event.reason?.toString?.() || '';
      
      if (
        reason.includes('runtime.lastError') ||
        reason.includes('Could not establish connection') ||
        reason.includes('Receiving end does not exist') ||
        reason.includes('Extension context invalidated') ||
        reason.includes('chrome-extension://')
      ) {
        event.preventDefault();
        return false;
      }
    };

    // 注册事件监听器
    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    // 清理函数
    return () => {
      // 恢复原始的console方法
      console.warn = originalWarn;
      console.error = originalError;
      
      // 移除事件监听器
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  // 这个组件不渲染任何内容
  return null;
}

/**
 * 安全的Chrome API调用包装器
 * 用于在可能存在Chrome扩展的环境中安全调用Chrome API
 */
export function safeChromeCall(callback: () => void): void {
  try {
    if (typeof window !== 'undefined' && typeof chrome !== 'undefined' && chrome.runtime) {
      callback();
    }
  } catch (error) {
    // 静默忽略Chrome API相关错误
    if (process.env.NODE_ENV === 'development') {
      console.debug('Chrome API call failed (suppressed):', error);
    }
  }
}

/**
 * 检查是否在Chrome扩展环境中
 */
export function isChromeExtensionContext(): boolean {
  try {
    return (
      typeof window !== 'undefined' &&
      typeof chrome !== 'undefined' &&
      chrome.runtime &&
      chrome.runtime.id !== undefined
    );
  } catch {
    return false;
  }
}
